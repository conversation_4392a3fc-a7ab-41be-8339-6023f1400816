"""
Exports API Blueprint

Handles export endpoints for generating reports and documentation.
"""

from flask import Blueprint, request, jsonify, send_file
import structlog
import io
import json
import os
import tempfile
from datetime import datetime
from typing import Dict, Any

# PDF generation imports
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.platypus.flowables import HRFlowable
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT

# Excel generation imports
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter

logger = structlog.get_logger()

exports_bp = Blueprint('exports', __name__)

# Constants
TEMP_DIR = tempfile.gettempdir()
MAX_FILE_AGE_HOURS = 24
ERROR_NO_DATA = 'No data provided'
ERROR_EXPORT_FAILED = 'Export failed'
DEFAULT_PROJECT_NAME = 'Untitled Project'

@exports_bp.route('/pdf', methods=['POST'])
def export_pdf():
    """
    Export calculation results to PDF format.

    Expected input:
    {
        "project_name": str,
        "calculations": dict,
        "template": str  # "standard", "detailed", "summary"
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': ERROR_NO_DATA}), 400

        project_name = data.get('project_name', DEFAULT_PROJECT_NAME)
        calculations = data.get('calculations', {})
        template = data.get('template', 'standard')

        # Generate PDF using reportlab
        pdf_buffer = io.BytesIO()
        pdf_filename = f"{project_name.replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"

        # Create PDF document
        doc = SimpleDocTemplate(pdf_buffer, pagesize=letter, topMargin=1*inch, bottomMargin=1*inch)
        story = []
        styles = getSampleStyleSheet()

        # Add custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        )

        # Build PDF content
        story.extend(_build_pdf_content(project_name, calculations, template, styles, title_style))

        # Build PDF
        doc.build(story)
        pdf_buffer.seek(0)

        # Save to temporary file for download
        temp_path = os.path.join(TEMP_DIR, pdf_filename)
        with open(temp_path, 'wb') as f:
            f.write(pdf_buffer.getvalue())

        file_size = len(pdf_buffer.getvalue())

        result = {
            'status': 'success',
            'message': 'PDF export generated successfully',
            'project_name': project_name,
            'template': template,
            'timestamp': datetime.now().isoformat(),
            'file_size': f'{file_size / 1024:.1f} KB',
            'filename': pdf_filename,
            'download_url': f'/api/exports/download/pdf/{pdf_filename}'
        }

        logger.info("PDF export completed",
                   project_name=project_name,
                   template=template,
                   file_size=file_size)
        return jsonify(result)

    except Exception as e:
        logger.error("PDF export failed", error=str(e))
        return jsonify({'error': ERROR_EXPORT_FAILED, 'message': str(e)}), 500

def _build_pdf_content(project_name: str, calculations: Dict[str, Any], template: str, styles, title_style) -> list:
    """Build PDF content based on template and calculations."""
    story = []

    # Title
    story.append(Paragraph(f"HVAC Design Report: {project_name}", title_style))
    story.append(Spacer(1, 20))

    # Project information
    story.append(Paragraph("Project Information", styles['Heading2']))
    project_info = [
        ['Project Name:', project_name],
        ['Generated:', datetime.now().strftime('%B %d, %Y at %I:%M %p')],
        ['Template:', template.title()],
        ['Software:', 'SizeWise Suite v0.1.0']
    ]

    project_table = Table(project_info, colWidths=[2*inch, 4*inch])
    project_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
    ]))
    story.append(project_table)
    story.append(Spacer(1, 20))

    # Calculations section
    if calculations:
        story.append(Paragraph("Calculation Results", styles['Heading2']))
        story.append(Spacer(1, 12))

        for calc_id, calc_data in calculations.items():
            story.extend(_build_calculation_section(calc_id, calc_data, styles, template))
            story.append(Spacer(1, 15))

    # Standards compliance section
    story.append(Paragraph("Standards Compliance", styles['Heading2']))
    story.append(Paragraph("This design has been validated against the following standards:", styles['Normal']))

    standards_list = [
        "• SMACNA HVAC Duct Construction Standards (2006)",
        "• NFPA 96 Standard for Ventilation Control and Fire Protection (2021)",
        "• ASHRAE Fundamentals (2021)"
    ]

    for standard in standards_list:
        story.append(Paragraph(standard, styles['Normal']))

    story.append(Spacer(1, 20))
    story.append(HRFlowable(width="100%", thickness=1, lineCap='round', color=colors.grey))
    story.append(Spacer(1, 10))
    story.append(Paragraph("Generated by SizeWise Suite - Professional HVAC Design Software",
                          ParagraphStyle('Footer', parent=styles['Normal'], fontSize=8, alignment=TA_CENTER)))

    return story

def _build_calculation_section(calc_id: str, calc_data: Dict[str, Any], styles, template: str) -> list:
    """Build a calculation section for the PDF."""
    section = []

    # Calculation title
    calc_name = calc_data.get('name', f'Calculation {calc_id}')
    section.append(Paragraph(calc_name, styles['Heading3']))
    section.append(Spacer(1, 8))

    # Input parameters
    if 'input' in calc_data:
        section.append(Paragraph("Input Parameters:", styles['Heading4']))
        input_data = calc_data['input']

        input_table_data = []
        for key, value in input_data.items():
            if isinstance(value, dict):
                value_str = ', '.join([f"{k}: {v}" for k, v in value.items()])
            else:
                value_str = str(value)
            input_table_data.append([key.replace('_', ' ').title(), value_str])

        if input_table_data:
            input_table = Table(input_table_data, colWidths=[2*inch, 3*inch])
            input_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ]))
            section.append(input_table)
            section.append(Spacer(1, 10))

    # Results
    if 'results' in calc_data:
        section.append(Paragraph("Results:", styles['Heading4']))
        results_data = calc_data['results']

        results_table_data = []
        for key, value in results_data.items():
            if isinstance(value, dict) and 'value' in value:
                unit = value.get('unit', '')
                value_str = f"{value['value']} {unit}".strip()
            else:
                value_str = str(value)
            results_table_data.append([key.replace('_', ' ').title(), value_str])

        if results_table_data:
            results_table = Table(results_table_data, colWidths=[2*inch, 3*inch])
            results_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                ('BACKGROUND', (0, 0), (0, -1), colors.lightblue),
            ]))
            section.append(results_table)

    return section

@exports_bp.route('/excel', methods=['POST'])
def export_excel():
    """
    Export calculation results to Excel format.

    Expected input:
    {
        "project_name": str,
        "calculations": dict,
        "template": str  # "standard", "detailed"
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': ERROR_NO_DATA}), 400

        project_name = data.get('project_name', DEFAULT_PROJECT_NAME)
        calculations = data.get('calculations', {})
        template = data.get('template', 'standard')

        # Generate Excel using openpyxl
        workbook = Workbook()
        excel_filename = f"{project_name.replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        # Remove default worksheet and create custom ones
        workbook.remove(workbook.active)

        # Create worksheets
        summary_ws = workbook.create_sheet("Project Summary")
        calculations_ws = workbook.create_sheet("Calculations")

        # Build Excel content
        _build_excel_summary(summary_ws, project_name, calculations, template)
        _build_excel_calculations(calculations_ws, calculations)

        # Save to temporary file
        temp_path = os.path.join(TEMP_DIR, excel_filename)
        workbook.save(temp_path)

        # Get file size
        file_size = os.path.getsize(temp_path)

        result = {
            'status': 'success',
            'message': 'Excel export generated successfully',
            'project_name': project_name,
            'template': template,
            'timestamp': datetime.now().isoformat(),
            'file_size': f'{file_size / 1024:.1f} KB',
            'filename': excel_filename,
            'download_url': f'/api/exports/download/excel/{excel_filename}'
        }

        logger.info("Excel export completed",
                   project_name=project_name,
                   template=template,
                   file_size=file_size)
        return jsonify(result)

    except Exception as e:
        logger.error("Excel export failed", error=str(e))
        return jsonify({'error': ERROR_EXPORT_FAILED, 'message': str(e)}), 500

def _build_excel_summary(worksheet, project_name: str, calculations: Dict[str, Any], template: str):
    """Build the summary worksheet for Excel export."""
    # Define styles
    header_font = Font(bold=True, size=14, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    title_font = Font(bold=True, size=16)

    # Title
    worksheet['A1'] = f"HVAC Design Report: {project_name}"
    worksheet['A1'].font = title_font
    worksheet.merge_cells('A1:D1')

    # Project information
    worksheet['A3'] = "Project Information"
    worksheet['A3'].font = header_font
    worksheet['A3'].fill = header_fill
    worksheet.merge_cells('A3:B3')

    info_data = [
        ("Project Name:", project_name),
        ("Generated:", datetime.now().strftime('%B %d, %Y at %I:%M %p')),
        ("Template:", template.title()),
        ("Software:", "SizeWise Suite v0.1.0")
    ]

    for i, (label, value) in enumerate(info_data, start=4):
        worksheet[f'A{i}'] = label
        worksheet[f'B{i}'] = value
        worksheet[f'A{i}'].font = Font(bold=True)

    # Summary statistics
    if calculations:
        worksheet['A9'] = "Calculation Summary"
        worksheet['A9'].font = header_font
        worksheet['A9'].fill = header_fill
        worksheet.merge_cells('A9:B9')

        worksheet['A10'] = "Total Calculations:"
        worksheet['B10'] = len(calculations)
        worksheet['A10'].font = Font(bold=True)

    # Auto-adjust column widths
    for column in worksheet.columns:
        max_length = 0
        column_letter = get_column_letter(column[0].column)
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        worksheet.column_dimensions[column_letter].width = adjusted_width

def _build_excel_calculations(worksheet, calculations: Dict[str, Any]):
    """Build the calculations worksheet for Excel export."""
    # Headers
    headers = ['Calculation ID', 'Parameter', 'Value', 'Unit', 'Type']
    for col, header in enumerate(headers, start=1):
        cell = worksheet.cell(row=1, column=col, value=header)
        cell.font = Font(bold=True, color="FFFFFF")
        cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")

    row = 2
    for calc_id, calc_data in calculations.items():
        # Input parameters
        if 'input' in calc_data:
            for param, value in calc_data['input'].items():
                worksheet.cell(row=row, column=1, value=calc_id)
                worksheet.cell(row=row, column=2, value=param)
                worksheet.cell(row=row, column=3, value=str(value))
                worksheet.cell(row=row, column=4, value='')
                worksheet.cell(row=row, column=5, value='Input')
                row += 1

        # Results
        if 'results' in calc_data:
            for param, value in calc_data['results'].items():
                worksheet.cell(row=row, column=1, value=calc_id)
                worksheet.cell(row=row, column=2, value=param)

                if isinstance(value, dict) and 'value' in value:
                    worksheet.cell(row=row, column=3, value=value['value'])
                    worksheet.cell(row=row, column=4, value=value.get('unit', ''))
                else:
                    worksheet.cell(row=row, column=3, value=str(value))
                    worksheet.cell(row=row, column=4, value='')

                worksheet.cell(row=row, column=5, value='Result')
                row += 1

    # Auto-adjust column widths
    for column in worksheet.columns:
        max_length = 0
        column_letter = get_column_letter(column[0].column)
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 30)
        worksheet.column_dimensions[column_letter].width = adjusted_width

@exports_bp.route('/csv', methods=['POST'])
def export_csv():
    """
    Export calculation results to CSV format.

    Expected input:
    {
        "project_name": str,
        "calculations": dict
    }
    """
    try:
        import csv

        data = request.get_json()

        if not data:
            return jsonify({'error': ERROR_NO_DATA}), 400

        project_name = data.get('project_name', DEFAULT_PROJECT_NAME)
        calculations = data.get('calculations', {})

        # Generate CSV
        csv_filename = f"{project_name.replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        temp_path = os.path.join(TEMP_DIR, csv_filename)

        with open(temp_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)

            # Write header
            writer.writerow(['Project', 'Calculation ID', 'Parameter', 'Value', 'Unit', 'Type'])

            # Write data
            for calc_id, calc_data in calculations.items():
                # Input parameters
                if 'input' in calc_data:
                    for param, value in calc_data['input'].items():
                        writer.writerow([project_name, calc_id, param, str(value), '', 'Input'])

                # Results
                if 'results' in calc_data:
                    for param, value in calc_data['results'].items():
                        if isinstance(value, dict) and 'value' in value:
                            writer.writerow([project_name, calc_id, param, value['value'], value.get('unit', ''), 'Result'])
                        else:
                            writer.writerow([project_name, calc_id, param, str(value), '', 'Result'])

        # Get file size
        file_size = os.path.getsize(temp_path)

        result = {
            'status': 'success',
            'message': 'CSV export generated successfully',
            'project_name': project_name,
            'timestamp': datetime.now().isoformat(),
            'file_size': f'{file_size / 1024:.1f} KB',
            'filename': csv_filename,
            'download_url': f'/api/exports/download/csv/{csv_filename}'
        }

        logger.info("CSV export completed", project_name=project_name, file_size=file_size)
        return jsonify(result)

    except Exception as e:
        logger.error("CSV export failed", error=str(e))
        return jsonify({'error': ERROR_EXPORT_FAILED, 'message': str(e)}), 500

@exports_bp.route('/json', methods=['POST'])
def export_json():
    """
    Export calculation results to JSON format.
    """
    try:
        data = request.get_json()
        
        project_name = data.get('project_name', 'Untitled Project')
        calculations = data.get('calculations', {})
        
        # Create structured JSON export
        export_data = {
            'project': {
                'name': project_name,
                'created': datetime.now().isoformat(),
                'version': '0.1.0'
            },
            'calculations': calculations,
            'metadata': {
                'exported_by': 'SizeWise Suite',
                'export_format': 'json',
                'standards_version': {
                    'smacna': '2006',
                    'nfpa': '2021',
                    'ashrae': '2021'
                }
            }
        }
        
        # Convert to pretty-printed JSON
        json_output = json.dumps(export_data, indent=2, sort_keys=True)
        
        result = {
            'status': 'success',
            'data': export_data,
            'download_content': json_output,
            'file_size': f'{len(json_output)} bytes'
        }
        
        logger.info("JSON export completed", project_name=project_name)
        return jsonify(result)
        
    except Exception as e:
        logger.error("JSON export failed", error=str(e))
        return jsonify({'error': 'Export failed', 'message': str(e)}), 500

@exports_bp.route('/formats', methods=['GET'])
def get_export_formats():
    """
    Get available export formats and their capabilities.
    """
    formats = {
        'pdf': {
            'name': 'PDF Report',
            'description': 'Professional report with calculations and diagrams',
            'templates': ['standard', 'detailed', 'summary'],
            'file_extension': '.pdf',
            'mime_type': 'application/pdf'
        },
        'excel': {
            'name': 'Excel Workbook',
            'description': 'Spreadsheet with calculations and data tables',
            'templates': ['standard', 'detailed'],
            'file_extension': '.xlsx',
            'mime_type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        },
        'csv': {
            'name': 'CSV Data',
            'description': 'Comma-separated values for data analysis',
            'templates': ['data_only'],
            'file_extension': '.csv',
            'mime_type': 'text/csv'
        },
        'json': {
            'name': 'JSON Data',
            'description': 'Structured data for API integration',
            'templates': ['standard'],
            'file_extension': '.json',
            'mime_type': 'application/json'
        }
    }
    
    return jsonify({
        'available_formats': formats,
        'default_format': 'pdf'
    })

@exports_bp.route('/download/<file_type>/<filename>', methods=['GET'])
def download_file(file_type: str, filename: str):
    """
    Download exported files.

    Args:
        file_type: Type of file (pdf, excel, csv)
        filename: Name of the file to download
    """
    try:
        # Validate file type
        valid_types = ['pdf', 'excel', 'csv']
        if file_type not in valid_types:
            return jsonify({'error': 'Invalid file type'}), 400

        # Construct file path
        file_path = os.path.join(TEMP_DIR, filename)

        # Check if file exists
        if not os.path.exists(file_path):
            return jsonify({'error': 'File not found'}), 404

        # Check file age (cleanup old files)
        file_age_hours = (datetime.now().timestamp() - os.path.getmtime(file_path)) / 3600
        if file_age_hours > MAX_FILE_AGE_HOURS:
            os.remove(file_path)
            return jsonify({'error': 'File expired'}), 410

        # Determine MIME type
        mime_types = {
            'pdf': 'application/pdf',
            'excel': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'csv': 'text/csv'
        }

        mime_type = mime_types.get(file_type, 'application/octet-stream')

        logger.info("File download requested", filename=filename, file_type=file_type)

        return send_file(
            file_path,
            as_attachment=True,
            download_name=filename,
            mimetype=mime_type
        )

    except Exception as e:
        logger.error("File download failed", error=str(e), filename=filename)
        return jsonify({'error': 'Download failed', 'message': str(e)}), 500

@exports_bp.route('/cleanup', methods=['POST'])
def cleanup_temp_files():
    """
    Clean up temporary export files older than MAX_FILE_AGE_HOURS.
    """
    try:
        cleaned_count = 0
        current_time = datetime.now().timestamp()

        for filename in os.listdir(TEMP_DIR):
            if filename.endswith(('.pdf', '.xlsx', '.csv')):
                file_path = os.path.join(TEMP_DIR, filename)
                file_age_hours = (current_time - os.path.getmtime(file_path)) / 3600

                if file_age_hours > MAX_FILE_AGE_HOURS:
                    try:
                        os.remove(file_path)
                        cleaned_count += 1
                    except OSError:
                        pass  # File might be in use or already deleted

        logger.info("Temporary files cleaned up", count=cleaned_count)
        return jsonify({
            'status': 'success',
            'cleaned_files': cleaned_count,
            'message': f'Cleaned up {cleaned_count} expired files'
        })

    except Exception as e:
        logger.error("Cleanup failed", error=str(e))
        return jsonify({'error': 'Cleanup failed', 'message': str(e)}), 500
