"""
Validation API Blueprint

Handles validation endpoints for HVAC standards compliance.
"""

from flask import Blueprint, request, jsonify
import sys
import os
import structlog

# Add project root to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from core.validation.hvac_validator import HVACValidator
from core.validation.units_validator import UnitsValidator

logger = structlog.get_logger()

validation_bp = Blueprint('validation', __name__)

# Initialize validators
hvac_validator = HVACValidator()
units_validator = UnitsValidator()

# Constants
ERROR_NO_DATA = 'No data provided'
ERROR_VALIDATION_FAILED = 'Validation failed'

@validation_bp.route('/smacna', methods=['POST'])
def validate_smacna():
    """
    Validate design against SMACNA standards.

    Expected input:
    {
        "duct_type": str,
        "dimensions": dict,
        "velocity": float,
        "friction_rate": float,
        "airflow": float,
        "room_type": str (optional),
        "units": str (optional, default: "imperial")
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': ERROR_NO_DATA}), 400

        # Perform SMACNA validation using the existing validator
        validation_result = hvac_validator.validate_smacna_compliance(data)

        # Convert ValidationResult to API response format
        result = {
            'input': data,
            'validation': {
                'compliant': validation_result.is_valid,
                'standard': 'SMACNA',
                'version': '2006',
                'checks': _build_smacna_checks(data),
                'warnings': validation_result.warnings,
                'errors': validation_result.errors
            }
        }

        logger.info("SMACNA validation completed",
                   input_data=data,
                   compliant=validation_result.is_valid,
                   warnings_count=len(validation_result.warnings),
                   errors_count=len(validation_result.errors))
        return jsonify(result)

    except Exception as e:
        logger.error("SMACNA validation failed", error=str(e))
        return jsonify({'error': ERROR_VALIDATION_FAILED, 'message': str(e)}), 500

def _build_smacna_checks(data: dict) -> list:
    """Build detailed SMACNA compliance checks for API response."""
    checks = []

    # Velocity check
    velocity = data.get('velocity', 0)
    if velocity > 0:
        smacna_limits = hvac_validator.standards['smacna']
        velocity_passed = (smacna_limits['min_velocity']['supply'] <= velocity <= smacna_limits['max_velocity']['supply'])
        checks.append({
            'parameter': 'velocity',
            'value': velocity,
            'limit_min': smacna_limits['min_velocity']['supply'],
            'limit_max': smacna_limits['max_velocity']['supply'],
            'unit': 'FPM',
            'passed': velocity_passed,
            'description': 'Air velocity within SMACNA recommended range'
        })

    # Friction rate check
    friction_rate = data.get('friction_rate', 0)
    if friction_rate > 0:
        smacna_limits = hvac_validator.standards['smacna']
        friction_passed = (smacna_limits['min_friction_rate'] <= friction_rate <= smacna_limits['max_friction_rate'])
        checks.append({
            'parameter': 'friction_rate',
            'value': friction_rate,
            'limit_min': smacna_limits['min_friction_rate'],
            'limit_max': smacna_limits['max_friction_rate'],
            'unit': 'in. w.g./100 ft',
            'passed': friction_passed,
            'description': 'Friction rate within SMACNA recommended range'
        })

    # Aspect ratio check for rectangular ducts
    if data.get('duct_type') == 'rectangular':
        dimensions = data.get('dimensions', {})
        width = dimensions.get('width', 0)
        height = dimensions.get('height', 0)
        if width > 0 and height > 0:
            aspect_ratio = max(width, height) / min(width, height)
            aspect_passed = aspect_ratio <= 4.0
            checks.append({
                'parameter': 'aspect_ratio',
                'value': aspect_ratio,
                'limit_max': 4.0,
                'unit': ':1',
                'passed': aspect_passed,
                'description': 'Rectangular duct aspect ratio within SMACNA limits'
            })

    return checks

@validation_bp.route('/nfpa', methods=['POST'])
def validate_nfpa():
    """
    Validate design against NFPA 96 standards for grease ducts.

    Expected input:
    {
        "duct_type": str,
        "velocity": float,
        "slope": float (optional),
        "length": float (optional),
        "units": str (optional, default: "imperial")
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': ERROR_NO_DATA}), 400

        # Perform NFPA validation using the existing validator
        validation_result = hvac_validator.validate_nfpa_compliance(data)

        # Convert ValidationResult to API response format
        result = {
            'input': data,
            'validation': {
                'compliant': validation_result.is_valid,
                'standard': 'NFPA 96',
                'version': '2021',
                'checks': _build_nfpa_checks(data),
                'warnings': validation_result.warnings,
                'errors': validation_result.errors
            }
        }

        logger.info("NFPA validation completed",
                   input_data=data,
                   compliant=validation_result.is_valid,
                   warnings_count=len(validation_result.warnings),
                   errors_count=len(validation_result.errors))
        return jsonify(result)

    except Exception as e:
        logger.error("NFPA validation failed", error=str(e))
        return jsonify({'error': ERROR_VALIDATION_FAILED, 'message': str(e)}), 500

def _build_nfpa_checks(data: dict) -> list:
    """Build detailed NFPA 96 compliance checks for API response."""
    checks = []

    # Only perform NFPA checks for grease ducts
    if data.get('duct_type') == 'grease':
        nfpa_limits = hvac_validator.standards['nfpa']['grease_duct']

        # Velocity check for grease ducts
        velocity = data.get('velocity', 0)
        if velocity > 0:
            velocity_passed = (nfpa_limits['min_velocity'] <= velocity <= nfpa_limits['max_velocity'])
            checks.append({
                'parameter': 'velocity',
                'value': velocity,
                'limit_min': nfpa_limits['min_velocity'],
                'limit_max': nfpa_limits['max_velocity'],
                'unit': 'FPM',
                'passed': velocity_passed,
                'description': 'Grease duct velocity within NFPA 96 requirements'
            })

        # Slope check
        slope = data.get('slope', 0)
        if slope >= 0:  # Allow zero slope for horizontal runs
            slope_passed = slope >= nfpa_limits['min_slope']
            checks.append({
                'parameter': 'slope',
                'value': slope,
                'limit_min': nfpa_limits['min_slope'],
                'unit': 'in/ft',
                'passed': slope_passed,
                'description': 'Grease duct slope meets NFPA 96 minimum requirements'
            })

        # Access panel spacing check
        length = data.get('length', 0)
        if length > 0:
            access_panels_needed = length > nfpa_limits['access_panel_spacing']
            checks.append({
                'parameter': 'access_panels',
                'value': length,
                'limit_max': nfpa_limits['access_panel_spacing'],
                'unit': 'ft',
                'passed': not access_panels_needed,
                'description': f'Access panels required every {nfpa_limits["access_panel_spacing"]} ft per NFPA 96'
            })

    return checks

@validation_bp.route('/ashrae', methods=['POST'])
def validate_ashrae():
    """
    Validate design against ASHRAE standards.

    Expected input:
    {
        "velocity": float,
        "location": str (optional, default: "unoccupied"),
        "room_type": str (optional, default: "office"),
        "duct_type": str (optional, default: "supply"),
        "units": str (optional, default: "imperial")
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': ERROR_NO_DATA}), 400

        # Perform ASHRAE validation using the existing validator
        validation_result = hvac_validator.validate_ashrae_compliance(data)

        # Also perform enhanced velocity validation if room_type and duct_type are provided
        enhanced_validation = None
        if data.get('room_type') and data.get('duct_type'):
            enhanced_validation = hvac_validator.validate_velocity_enhanced(
                data.get('velocity', 0),
                data.get('room_type', 'office'),
                data.get('duct_type', 'supply')
            )

        # Convert ValidationResult to API response format
        result = {
            'input': data,
            'validation': {
                'compliant': validation_result.is_valid and (enhanced_validation is None or enhanced_validation['compliant']),
                'standard': 'ASHRAE',
                'version': '2021',
                'checks': _build_ashrae_checks(data, enhanced_validation),
                'warnings': validation_result.warnings + (enhanced_validation['warnings'] if enhanced_validation else []),
                'errors': validation_result.errors + (enhanced_validation['errors'] if enhanced_validation else [])
            }
        }

        logger.info("ASHRAE validation completed",
                   input_data=data,
                   compliant=result['validation']['compliant'],
                   warnings_count=len(result['validation']['warnings']),
                   errors_count=len(result['validation']['errors']))
        return jsonify(result)

    except Exception as e:
        logger.error("ASHRAE validation failed", error=str(e))
        return jsonify({'error': ERROR_VALIDATION_FAILED, 'message': str(e)}), 500

def _build_ashrae_checks(data: dict, enhanced_validation: dict = None) -> list:
    """Build detailed ASHRAE compliance checks for API response."""
    checks = []

    velocity = data.get('velocity', 0)
    location = data.get('location', 'unoccupied')

    if velocity > 0:
        ashrae_limits = hvac_validator.standards['ashrae']['comfort_velocity']

        # Basic comfort velocity check
        if location == 'occupied':
            limit = ashrae_limits['occupied_zone']
            passed = velocity <= limit
            checks.append({
                'parameter': 'comfort_velocity_occupied',
                'value': velocity,
                'limit_max': limit,
                'unit': 'FPM',
                'passed': passed,
                'description': 'Air velocity in occupied zone within ASHRAE comfort limits'
            })
        else:
            limit = ashrae_limits['unoccupied_zone']
            passed = velocity <= limit
            checks.append({
                'parameter': 'comfort_velocity_unoccupied',
                'value': velocity,
                'limit_max': limit,
                'unit': 'FPM',
                'passed': passed,
                'description': 'Air velocity in unoccupied zone within ASHRAE comfort limits'
            })

    # Enhanced velocity validation checks
    if enhanced_validation:
        room_type = data.get('room_type', 'office')
        duct_type = data.get('duct_type', 'supply')

        checks.append({
            'parameter': 'enhanced_velocity',
            'value': velocity,
            'room_type': room_type,
            'duct_type': duct_type,
            'unit': 'FPM',
            'passed': enhanced_validation['compliant'],
            'description': f'Air velocity for {room_type} {duct_type} duct per ASHRAE 2021 Chapter 21',
            'standard_reference': enhanced_validation.get('standard_reference', 'ASHRAE 2021')
        })

    return checks

@validation_bp.route('/units', methods=['POST'])
def validate_units():
    """
    Validate and convert units between Imperial and Metric systems.

    Expected input:
    {
        "values": dict,  # Dictionary of parameter: value pairs
        "from_units": str,  # "imperial" or "metric"
        "to_units": str (optional),  # Target units for conversion
        "parameters": list (optional)  # Specific parameters to validate/convert
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': ERROR_NO_DATA}), 400

        # Perform unit validation using the existing validator
        validation_result = units_validator.validate_units(data)

        # Perform conversions if requested
        conversions = {}
        if data.get('to_units') and data.get('to_units') != data.get('from_units'):
            try:
                conversions = units_validator.convert_values(
                    data.get('values', {}),
                    data.get('from_units', 'imperial'),
                    data.get('to_units', 'metric')
                )
            except Exception as conv_error:
                logger.warning("Unit conversion failed", error=str(conv_error))
                validation_result.add_warning(f"Unit conversion failed: {str(conv_error)}")

        # Convert ValidationResult to API response format
        result = {
            'input': data,
            'validation': {
                'valid_units': validation_result.is_valid,
                'conversions': conversions,
                'warnings': validation_result.warnings,
                'errors': validation_result.errors,
                'supported_units': ['imperial', 'metric'],
                'supported_parameters': units_validator.get_supported_parameters()
            }
        }

        logger.info("Unit validation completed",
                   input_data=data,
                   valid=validation_result.is_valid,
                   conversions_count=len(conversions))
        return jsonify(result)

    except Exception as e:
        logger.error("Unit validation failed", error=str(e))
        return jsonify({'error': ERROR_VALIDATION_FAILED, 'message': str(e)}), 500
